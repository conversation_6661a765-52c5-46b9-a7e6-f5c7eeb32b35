{"name": "react-offline-maps", "displayName": "react-offline-maps", "description": "A simple offline maps component for React", "version": "0.1.5", "license": "MIT", "main": "dist/index.js", "typings": "dist/index.d.ts", "files": ["dist", "LICENSE", "README.md"], "engines": {"node": ">=10"}, "scripts": {"start": "tsdx watch", "build": "tsdx build", "test": "tsdx test --passWithNoTests", "lint": "tsdx lint", "prepare": "tsdx build", "size": "size-limit", "analyze": "size-limit --why"}, "peerDependencies": {"react": ">=16"}, "husky": {"hooks": {"pre-commit": "tsdx lint"}}, "prettier": {"printWidth": 80, "semi": true, "singleQuote": true, "trailingComma": "es5"}, "author": "<PERSON><PERSON>", "module": "dist/mylib.esm.js", "size-limit": [{"path": "dist/mylib.cjs.production.min.js", "limit": "10 KB"}, {"path": "dist/mylib.esm.js", "limit": "10 KB"}], "devDependencies": {"@size-limit/preset-small-lib": "^11.1.2", "@types/react": "^18.3.1", "@types/react-dom": "^18.3.0", "autoprefixer": "^10.4.19", "husky": "^9.0.11", "postcss": "^8.4.38", "react": "^18.3.1", "react-dom": "^18.3.1", "rollup-plugin-postcss": "^4.0.2", "size-limit": "^11.1.2", "tailwindcss": "^3.4.3", "tsdx": "^0.14.1", "tslib": "^2.6.2", "typescript": "^5.4.5"}, "repository": {"type": "git", "url": "git+https://github.com/flatypus/react-offline-maps.git"}, "keywords": ["react", "typescript", "tailwind", "leaflet", "offline", "cache", "maps", "google maps", "openstreetmap"]}